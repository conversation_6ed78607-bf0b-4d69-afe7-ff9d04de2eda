#!/bin/bash

# 华为地图Vue3组件发布脚本
# 使用方法: ./scripts/publish.sh

set -e

echo "🚀 开始发布华为地图Vue3组件到NPM..."

# 检查是否已登录NPM
echo "📋 检查NPM登录状态..."
if ! npm whoami > /dev/null 2>&1; then
    echo "❌ 请先登录NPM: npm login"
    exit 1
fi

echo "✅ NPM登录状态正常"

# 检查包名是否可用
echo "📦 检查包名可用性..."
if npm view huawei-map-vue3 > /dev/null 2>&1; then
    echo "⚠️  包名 'huawei-map-vue3' 已存在，请检查版本号"
else
    echo "✅ 包名可用"
fi

# 运行测试
echo "🧪 运行测试..."
npm run test:unit || {
    echo "❌ 测试失败，请修复后再发布"
    exit 1
}

# 代码检查
echo "🔍 运行代码检查..."
npm run lint || {
    echo "❌ 代码检查失败，请修复后再发布"
    exit 1
}

# 构建项目
echo "🔨 构建项目..."
npm run build || {
    echo "❌ 构建失败"
    exit 1
}

# 检查构建产物
echo "📁 检查构建产物..."
if [ ! -f "dist/index.d.ts" ]; then
    echo "❌ 类型定义文件缺失"
    exit 1
fi

if [ ! -f "dist/huawei-map-vue3.js" ]; then
    echo "❌ ES模块文件缺失"
    exit 1
fi

if [ ! -f "dist/huawei-map-vue3.umd.cjs" ]; then
    echo "❌ UMD模块文件缺失"
    exit 1
fi

echo "✅ 构建产物检查通过"

# 显示即将发布的信息
echo "📋 发布信息:"
echo "  包名: $(node -p "require('./package.json').name")"
echo "  版本: $(node -p "require('./package.json').version")"
echo "  作者: $(node -p "require('./package.json').author.name")"

# 确认发布
read -p "🤔 确认发布? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 发布已取消"
    exit 1
fi

# 发布到NPM
echo "📤 发布到NPM..."
npm publish || {
    echo "❌ 发布失败"
    exit 1
}

echo "🎉 发布成功!"
echo "📦 包地址: https://www.npmjs.com/package/huawei-map-vue3"
echo "📚 安装命令: npm install huawei-map-vue3"

# 可选：创建Git标签
read -p "🏷️  是否创建Git标签? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    VERSION=$(node -p "require('./package.json').version")
    git tag -a "v$VERSION" -m "Release v$VERSION"
    echo "✅ 已创建标签 v$VERSION"
    
    read -p "📤 是否推送标签到远程仓库? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git push origin "v$VERSION"
        echo "✅ 标签已推送到远程仓库"
    fi
fi

echo "🎊 发布流程完成!"
