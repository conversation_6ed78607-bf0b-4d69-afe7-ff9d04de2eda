<template>
  <div class="component-test">
    <header class="test-header">
      <h1>华为地图 Vue3 组件测试页面</h1>
      <p>测试所有组件的功能和集成效果</p>
    </header>

    <nav class="test-nav">
      <button
        v-for="test in tests"
        :key="test.id"
        :class="{ active: activeTest === test.id }"
        @click="activeTest = test.id"
      >
        {{ test.name }}
      </button>
    </nav>

    <main class="test-content">
      <!-- 基础地图测试 -->
      <div v-if="activeTest === 'basic'" class="test-section">
        <h2>基础地图组件测试</h2>
        <div class="test-grid">
          <div class="test-item">
            <h3>默认配置</h3>
            <HuaweiMap
              width="100%"
              height="300px"
              @map-ready="onMapReady"
              @map-error="onMapError"
            />
          </div>

          <div class="test-item">
            <h3>自定义配置</h3>
            <HuaweiMap
              :center="{ lat: 39.9042, lng: 116.4074 }"
              :zoom="12"
              width="100%"
              height="300px"
              source-type="vector"
              map-type="TERRAIN"
              :zoom-control="true"
              :scale-control="true"
              @map-ready="onMapReady"
            />
          </div>
        </div>
      </div>

      <!-- 标记功能测试 -->
      <div v-if="activeTest === 'markers'" class="test-section">
        <h2>标记功能测试</h2>
        <div class="test-controls">
          <button @click="addRandomMarker">添加随机标记</button>
          <button @click="clearAllMarkers">清空所有标记</button>
          <button @click="addBatchMarkers">批量添加标记</button>
        </div>
        <HuaweiMap
          ref="markerTestMap"
          :center="{ lat: 39.9042, lng: 116.4074 }"
          :zoom="10"
          width="100%"
          height="400px"
          @map-ready="onMarkerMapReady"
          @marker-click="onMarkerClick"
        />
        <div class="test-info">
          <p>标记数量: {{ markerCount }}</p>
          <p>最后点击的标记: {{ lastClickedMarker || '无' }}</p>
        </div>
      </div>

      <!-- 信息窗测试 -->
      <div v-if="activeTest === 'infowindows'" class="test-section">
        <h2>信息窗功能测试</h2>
        <div class="test-controls">
          <button @click="addMarkerWithInfo">添加带信息窗的标记</button>
          <button @click="openAllInfoWindows">打开所有信息窗</button>
          <button @click="closeAllInfoWindows">关闭所有信息窗</button>
        </div>
        <HuaweiMap
          ref="infoWindowTestMap"
          :center="{ lat: 31.2304, lng: 121.4737 }"
          :zoom="11"
          width="100%"
          height="400px"
          @map-ready="onInfoWindowMapReady"
        />
      </div>

      <!-- 事件监控测试 -->
      <div v-if="activeTest === 'events'" class="test-section">
        <h2>事件监控测试</h2>
        <HuaweiMap
          :center="{ lat: 23.1291, lng: 113.2644 }"
          :zoom="10"
          width="100%"
          height="400px"
          :enable-event-monitor="true"
          @map-ready="onEventMapReady"
          @map-click="onMapClick"
          @map-dblclick="onMapDblClick"
          @center-changed="onCenterChanged"
          @zoom-changed="onZoomChanged"
        />
        <div class="event-log">
          <h3>事件日志</h3>
          <div class="log-container">
            <div v-for="(event, index) in eventLog" :key="index" class="log-item">
              <span class="log-time">{{ event.time }}</span>
              <span class="log-type">{{ event.type }}</span>
              <span class="log-data">{{ event.data }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置面板测试 -->
      <div v-if="activeTest === 'config'" class="test-section">
        <h2>配置面板测试</h2>
        <div class="config-test-layout">
          <div class="map-container">
            <HuaweiMap
              ref="configTestMap"
              :center="config.center"
              :zoom="config.zoom"
              :language="config.language"
              :source-type="config.sourceType"
              :map-type="config.mapType"
              :min-zoom="config.minZoom"
              :max-zoom="config.maxZoom"
              :copyright-control="config.controls?.copyright"
              :location-control="config.controls?.location"
              :navigation-control="config.controls?.navigation"
              :rotate-control="config.controls?.rotate"
              :scale-control="config.controls?.scale"
              :zoom-control="config.controls?.zoom"
              :zoom-slider="config.controls?.zoomSlider"
              :logo-position="config.style?.logoPosition"
              :preset-style-id="config.style?.presetStyleId"
              :opacity="config.style?.opacity"
              width="100%"
              height="400px"
              @map-ready="onConfigMapReady"
            />
          </div>
          <div class="config-panel">
            <MapConfigPanel
              :config="config"
              @config-change="onConfigChange"
            />
          </div>
        </div>
      </div>

      <!-- 集成测试 -->
      <div v-if="activeTest === 'integration'" class="test-section">
        <h2>完整功能集成测试</h2>
        <div class="integration-layout">
          <div class="map-section">
            <HuaweiMap
              ref="integrationMap"
              :center="integrationConfig.center"
              :zoom="integrationConfig.zoom"
              :enable-event-monitor="true"
              width="100%"
              height="500px"
              @map-ready="onIntegrationMapReady"
            />
          </div>
          <div class="control-panels">
            <MapConfigPanel
              :config="integrationConfig"
              @config-change="onIntegrationConfigChange"
            />
            <MarkerPanel
              :markers="integrationMarkers"
              :visible-markers="integrationVisibleMarkers"
              @add-marker="onAddIntegrationMarker"
              @remove-marker="onRemoveIntegrationMarker"
              @toggle-marker="onToggleIntegrationMarker"
            />
            <InfoWindowPanel
              :info-windows="integrationInfoWindows"
              @add-info-window="onAddIntegrationInfoWindow"
              @remove-info-window="onRemoveIntegrationInfoWindow"
            />
            <MapEventMonitor
              :event-history="integrationEventHistory"
              @clear-history="onClearIntegrationHistory"
            />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import HuaweiMap from '../components/HuaweiMap.vue'
import MapConfigPanel from '../components/MapConfigPanel.vue'
import MarkerPanel from '../components/MarkerPanel.vue'
import InfoWindowPanel from '../components/InfoWindowPanel.vue'
import MapEventMonitor from '../components/MapEventMonitor.vue'
import { useMapConfig } from '../composables/useMapConfig'

// 测试页面状态
const activeTest = ref('basic')

const tests = [
  { id: 'basic', name: '基础地图' },
  { id: 'markers', name: '标记功能' },
  { id: 'infowindows', name: '信息窗' },
  { id: 'events', name: '事件监控' },
  { id: 'config', name: '配置面板' },
  { id: 'integration', name: '集成测试' }
]

// 地图引用
const markerTestMap = ref()
const infoWindowTestMap = ref()
const configTestMap = ref()
const integrationMap = ref()

// 标记测试状态
const markerCount = ref(0)
const lastClickedMarker = ref('')

// 事件日志
const eventLog = ref<Array<{ time: string; type: string; data: string }>>([])

// 配置测试
const { config } = useMapConfig({
  center: { lat: 22.5431, lng: 114.0579 },
  zoom: 12
})

// 集成测试状态
const { config: integrationConfig } = useMapConfig({
  center: { lat: 30.2741, lng: 120.1551 },
  zoom: 10
})

const integrationMarkers = ref([])
const integrationVisibleMarkers = computed(() => integrationMarkers.value.filter(m => m.visible))
const integrationInfoWindows = ref([])
const integrationEventHistory = ref([])

// 事件处理函数
const onMapReady = (map: any) => {
  console.log('地图加载完成:', map)
  addEventLog('map-ready', '地图初始化完成')
}

const onMapError = (error: string) => {
  console.error('地图加载错误:', error)
  addEventLog('map-error', error)
}

const onMarkerMapReady = () => {
  addEventLog('marker-map-ready', '标记测试地图初始化完成')
}

const onMarkerClick = (marker: any, event: any) => {
  lastClickedMarker.value = marker.title || marker.id
  addEventLog('marker-click', `点击标记: ${marker.title || marker.id}`)
}

const onInfoWindowMapReady = () => {
  addEventLog('infowindow-map-ready', '信息窗测试地图初始化完成')
}

const onEventMapReady = () => {
  addEventLog('event-map-ready', '事件监控测试地图初始化完成')
}

const onConfigMapReady = () => {
  addEventLog('config-map-ready', '配置测试地图初始化完成')
}

const onIntegrationMapReady = () => {
  addEventLog('integration-map-ready', '集成测试地图初始化完成')
}

// 地图事件处理
const onMapClick = (event: any) => {
  addEventLog('map-click', `点击位置: ${event.latLng?.lat?.toFixed(6)}, ${event.latLng?.lng?.toFixed(6)}`)
}

const onMapDblClick = (event: any) => {
  addEventLog('map-dblclick', `双击位置: ${event.latLng?.lat?.toFixed(6)}, ${event.latLng?.lng?.toFixed(6)}`)
}

const onCenterChanged = (center: any) => {
  addEventLog('center-changed', `中心点变化: ${center.lat?.toFixed(6)}, ${center.lng?.toFixed(6)}`)
}

const onZoomChanged = (zoom: number) => {
  addEventLog('zoom-changed', `缩放级别变化: ${zoom}`)
}

// 配置变化处理
const onConfigChange = (newConfig: any) => {
  Object.assign(config, newConfig)
  addEventLog('config-change', '配置已更新')
}

const onIntegrationConfigChange = (newConfig: any) => {
  Object.assign(integrationConfig, newConfig)
  addEventLog('integration-config-change', '集成测试配置已更新')
}

// 标记操作
const addRandomMarker = () => {
  if (markerTestMap.value) {
    const lat = 39.9042 + (Math.random() - 0.5) * 0.1
    const lng = 116.4074 + (Math.random() - 0.5) * 0.1
    const id = markerTestMap.value.addMarker({
      position: { lat, lng },
      title: `随机标记 ${markerCount.value + 1}`,
      content: `这是第 ${markerCount.value + 1} 个随机标记`
    })
    markerCount.value++
    addEventLog('add-marker', `添加标记: ${id}`)
  }
}

const clearAllMarkers = () => {
  if (markerTestMap.value) {
    const count = markerTestMap.value.clearMarkers()
    markerCount.value = 0
    addEventLog('clear-markers', `清空了 ${count} 个标记`)
  }
}

const addBatchMarkers = () => {
  if (markerTestMap.value) {
    const markers = []
    for (let i = 0; i < 5; i++) {
      const lat = 39.9042 + (Math.random() - 0.5) * 0.2
      const lng = 116.4074 + (Math.random() - 0.5) * 0.2
      markers.push({
        position: { lat, lng },
        title: `批量标记 ${i + 1}`,
        content: `批量添加的第 ${i + 1} 个标记`
      })
    }
    markerTestMap.value.addMarkers(markers)
    markerCount.value += 5
    addEventLog('add-batch-markers', '批量添加了 5 个标记')
  }
}

// 信息窗操作
const addMarkerWithInfo = () => {
  if (infoWindowTestMap.value) {
    const lat = 31.2304 + (Math.random() - 0.5) * 0.1
    const lng = 121.4737 + (Math.random() - 0.5) * 0.1
    const markerId = infoWindowTestMap.value.addMarker({
      position: { lat, lng },
      title: '带信息窗的标记',
      content: '点击查看详细信息'
    })

    const infoWindowId = infoWindowTestMap.value.addInfoWindow({
      position: { lat, lng },
      content: `
        <div style="padding: 10px;">
          <h4>详细信息</h4>
          <p>位置: ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
          <p>创建时间: ${new Date().toLocaleString()}</p>
        </div>
      `,
      markerId
    })

    addEventLog('add-marker-with-info', `添加带信息窗的标记: ${markerId}, 信息窗: ${infoWindowId}`)
  }
}

const openAllInfoWindows = () => {
  if (infoWindowTestMap.value) {
    // 这里需要实现打开所有信息窗的逻辑
    addEventLog('open-all-infowindows', '打开所有信息窗')
  }
}

const closeAllInfoWindows = () => {
  if (infoWindowTestMap.value) {
    infoWindowTestMap.value.closeAllInfoWindows()
    addEventLog('close-all-infowindows', '关闭所有信息窗')
  }
}

// 集成测试操作
const onAddIntegrationMarker = (markerData: any) => {
  if (integrationMap.value) {
    const id = integrationMap.value.addMarker(markerData)
    integrationMarkers.value.push({ ...markerData, id, visible: true })
    addEventLog('integration-add-marker', `集成测试添加标记: ${id}`)
  }
}

const onRemoveIntegrationMarker = (markerId: string) => {
  if (integrationMap.value) {
    integrationMap.value.removeMarker(markerId)
    const index = integrationMarkers.value.findIndex(m => m.id === markerId)
    if (index > -1) {
      integrationMarkers.value.splice(index, 1)
    }
    addEventLog('integration-remove-marker', `集成测试删除标记: ${markerId}`)
  }
}

const onToggleIntegrationMarker = (markerId: string) => {
  const marker = integrationMarkers.value.find(m => m.id === markerId)
  if (marker) {
    marker.visible = !marker.visible
    if (integrationMap.value) {
      integrationMap.value.toggleMarkerVisibility(markerId)
    }
    addEventLog('integration-toggle-marker', `集成测试切换标记可见性: ${markerId}`)
  }
}

const onAddIntegrationInfoWindow = (infoWindowData: any) => {
  if (integrationMap.value) {
    const id = integrationMap.value.addInfoWindow(infoWindowData)
    integrationInfoWindows.value.push({ ...infoWindowData, id })
    addEventLog('integration-add-infowindow', `集成测试添加信息窗: ${id}`)
  }
}

const onRemoveIntegrationInfoWindow = (infoWindowId: string) => {
  if (integrationMap.value) {
    integrationMap.value.removeInfoWindow(infoWindowId)
    const index = integrationInfoWindows.value.findIndex(iw => iw.id === infoWindowId)
    if (index > -1) {
      integrationInfoWindows.value.splice(index, 1)
    }
    addEventLog('integration-remove-infowindow', `集成测试删除信息窗: ${infoWindowId}`)
  }
}

const onClearIntegrationHistory = () => {
  integrationEventHistory.value = []
  addEventLog('integration-clear-history', '清空集成测试事件历史')
}

// 工具函数
const addEventLog = (type: string, data: string) => {
  const time = new Date().toLocaleTimeString()
  eventLog.value.unshift({ time, type, data })
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}
</script>

<style scoped>
.component-test {
  min-height: 100vh;
  background: #f5f5f5;
}

.test-header {
  background: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  margin: 0 0 10px 0;
  color: #333;
}

.test-header p {
  margin: 0;
  color: #666;
}

.test-nav {
  background: white;
  padding: 15px 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  border-bottom: 1px solid #eee;
}

.test-nav button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.test-nav button:hover {
  background: #f0f0f0;
}

.test-nav button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.test-content {
  padding: 20px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-item {
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.test-item h3 {
  margin: 0;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #666;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-controls button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.test-controls button:hover {
  background: #0056b3;
}

.test-info {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.test-info p {
  margin: 5px 0;
  color: #666;
}

.event-log {
  margin-top: 20px;
}

.event-log h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #f8f9fa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  display: flex;
  gap: 10px;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-type {
  color: #007bff;
  min-width: 120px;
  font-weight: 500;
}

.log-data {
  color: #333;
  flex: 1;
}

.config-test-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 20px;
}

.config-panel {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
}

.integration-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
}

.map-section {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
}

.control-panels {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 600px;
  overflow-y: auto;
}

.control-panels > * {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
}

@media (max-width: 1200px) {
  .config-test-layout,
  .integration-layout {
    grid-template-columns: 1fr;
  }

  .test-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .test-content {
    padding: 10px;
  }

  .test-nav {
    padding: 10px;
  }

  .test-nav button {
    font-size: 12px;
    padding: 6px 12px;
  }
}
</style>
