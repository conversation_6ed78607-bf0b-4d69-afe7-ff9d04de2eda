// 华为地图 Vue3 组件库
// Huawei Map Vue3 Component Library

// 主要组件导出
export { default as HuaweiMap } from './components/HuaweiMap.vue'
export { default as MapConfigPanel } from './components/MapConfigPanel.vue'
export { default as MapEventMonitor } from './components/MapEventMonitor.vue'
export { default as MarkerPanel } from './components/MarkerPanel.vue'
export { default as InfoWindowPanel } from './components/InfoWindowPanel.vue'

// Composables 导出
export { useMapConfig } from './composables/useMapConfig'
export { useMapEvents } from './composables/useMapEvents'
export { useMapMarkers } from './composables/useMapMarkers'
export { useInfoWindows } from './composables/useInfoWindows'

// 类型定义导出
export type {
  MapConfigOptions
} from './composables/useMapConfig'

export type {
  MapEventHandlers,
  MapEventData
} from './composables/useMapEvents'

export type {
  MarkerData,
  MarkerEventHandlers
} from './composables/useMapMarkers'

export type {
  InfoWindowData,
  InfoWindowEventHandlers
} from './composables/useInfoWindows'

// 配置和工具导出
export { defaultMapConfig, mapStyles, mapTypes } from './config/map-config'
export { loadHuaweiMapAPI, isHuaweiMapAPILoaded } from './utils/map-loader'

// 华为地图 API 类型定义
export type { HuaweiMapConfig } from './config/map-config'

// 示例组件导出（可选）
export { default as BasicExample } from './examples/BasicExample.vue'
export { default as AdvancedExample } from './examples/AdvancedExample.vue'

// 版本信息
export const version = '1.0.0'
